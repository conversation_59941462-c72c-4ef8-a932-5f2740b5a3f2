<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 响应式视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="none"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            crossorigin="anonymous"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}">
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            您的浏览器不支持HTML5视频播放。
                        </video>
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="loading-text mt-2 text-white">正在加载视频...</div>
                        </div>
                        <!-- 错误状态 -->
                        <div class="video-error" id="video-error" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h5>视频加载失败</h5>
                            <p>请检查网络连接或稍后重试</p>
                            <button class="btn btn-primary" onclick="retryLoadVideo()">
                                <i class="fas fa-redo me-1"></i>重试
                            </button>
                        </div>








                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>

                    <!-- 移动端调试信息 -->
                    <div th:if="${isMobile}" class="alert alert-info mb-3">
                        <i class="fas fa-mobile-alt me-2"></i>
                        <strong>移动端模式</strong> - 已启用移动端优化
                    </div>

                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <script src="/js/mobile-video-optimizer.js"></script>


    <script th:inline="javascript">
        // 移动端视频播放器优化
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;
            const loadingElement = document.getElementById('video-loading');
            const errorElement = document.getElementById('video-error');

            // 检测移动设备
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

            console.log('设备检测:', { isMobile, isIOS, userAgent: navigator.userAgent });

            // 移动端优化配置
            if (isMobile) {
                videoElement.setAttribute('playsinline', 'true');
                videoElement.setAttribute('webkit-playsinline', 'true');
                if (isIOS) {
                    videoElement.setAttribute('muted', 'true'); // iOS需要静音才能自动播放
                }
            }

            // 设置视频源 - 延迟加载优化
            function loadVideo() {
                if (videoUrl) {
                    console.log('开始加载视频:', videoUrl);
                    showLoading();

                    // 清除现有源
                    videoElement.innerHTML = '';

                    // 创建新的source元素
                    const source = document.createElement('source');
                    source.src = videoUrl;
                    source.type = 'video/mp4';
                    videoElement.appendChild(source);

                    // 强制重新加载
                    videoElement.load();
                } else {
                    showError('视频地址无效');
                }
            }

            // 生成视频首帧缩略图
            function generateThumbnail() {
                if (!videoElement.poster) {
                    videoElement.addEventListener('loadeddata', function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 设置画布尺寸
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;

                            // 绘制视频首帧
                            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                            // 转换为base64图片
                            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                            videoElement.poster = thumbnailDataUrl;

                            console.log('视频首帧缩略图生成成功');
                        } catch (error) {
                            console.warn('无法生成视频缩略图:', error);
                        }
                    });
                }
            }

            // 显示/隐藏加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            }

            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示错误状态
            function showError(message) {
                hideLoading();
                if (errorElement) {
                    errorElement.style.display = 'flex';
                    const errorText = errorElement.querySelector('p');
                    if (errorText && message) {
                        errorText.textContent = message;
                    }
                }
                console.error('视频播放错误:', message);
            }

            // 重试加载视频
            window.retryLoadVideo = function() {
                console.log('重试加载视频');
                loadVideo();
            };

            // 网络状态检测
            function checkNetworkStatus() {
                if ('onLine' in navigator) {
                    if (!navigator.onLine) {
                        showError('网络连接已断开，请检查网络设置');
                        return false;
                    }
                }
                return true;
            }

            // 初始化加载状态
            showLoading();

            // 生成缩略图
            generateThumbnail();

            // 视频加载开始
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                showLoading();
            });

            // 视频元数据加载完成
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                hideLoading();
            });

            // 视频数据加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
                hideLoading();
            });

            // 视频可以播放
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                hideLoading();
            });

            // 错误处理 - 优化移动端
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);

                let errorMessage = '视频加载失败，请检查网络连接。';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频';
                            break;
                        case 3:
                            errorMessage = '视频解码失败或格式不支持';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问';
                            break;
                    }
                }

                // 移动端特殊处理
                if (isMobile) {
                    errorMessage += '，建议切换到WiFi网络重试';
                }

                showError(errorMessage);
            });

            // 网络状态变化监听
            window.addEventListener('online', function() {
                console.log('网络已连接');
                if (errorElement && errorElement.style.display !== 'none') {
                    loadVideo(); // 网络恢复时自动重试
                }
            });

            window.addEventListener('offline', function() {
                console.log('网络已断开');
                showError('网络连接已断开');
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            // 加载超时处理
            let loadTimeout;
            function setLoadTimeout() {
                clearTimeout(loadTimeout);
                loadTimeout = setTimeout(function() {
                    if (loadingElement && loadingElement.style.display !== 'none') {
                        console.warn('视频加载超时');
                        showError('视频加载超时，请检查网络连接或稍后重试');
                    }
                }, isMobile ? 30000 : 15000); // 移动端给更长的超时时间
            }

            // 在开始加载时设置超时
            videoElement.addEventListener('loadstart', function() {
                setLoadTimeout();
            });

            // 加载成功时清除超时
            videoElement.addEventListener('canplay', function() {
                clearTimeout(loadTimeout);
            });

            // 移动端触摸优化
            if (isMobile) {
                videoElement.addEventListener('touchstart', function(e) {
                    // 防止双击缩放
                    e.preventDefault();
                });

                // iOS Safari 特殊处理
                if (isIOS) {
                    videoElement.addEventListener('click', function() {
                        if (videoElement.paused) {
                            videoElement.play().catch(function(error) {
                                console.warn('自动播放失败:', error);
                                showError('请手动点击播放按钮开始播放');
                            });
                        }
                    });
                }
            }

            // 初始化移动端优化器
            let mobileOptimizer = null;
            if (window.MobileVideoOptimizer) {
                mobileOptimizer = new MobileVideoOptimizer(videoElement);

                // 显示优化建议
                const tips = mobileOptimizer.getOptimizationTips();
                if (tips.length > 0) {
                    console.log('移动端优化建议:', tips);
                }
            }

            // 初始加载视频
            if (checkNetworkStatus()) {
                loadVideo();
            }

            console.log('移动端优化视频播放器已准备就绪');
        });














    </script>



    <!-- 简单的视频播放器初始化 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const loadingElement = document.getElementById('video-loading');

            if (!videoElement) return;

            // 隐藏加载状态
            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
            }

            // 视频加载完成
            videoElement.addEventListener('loadeddata', function() {
                hideLoading();
                console.log('视频加载完成');
            });

            // 视频开始播放
            videoElement.addEventListener('play', function() {
                hideLoading();
                console.log('视频开始播放');
            });

            // 视频暂停
            videoElement.addEventListener('pause', function() {
                console.log('视频暂停');
            });

            // 视频结束
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束');
            });

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                hideLoading();
                console.error('视频播放出错:', e);

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>视频加载失败，请检查网络连接或稍后重试。';
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 等待事件
            videoElement.addEventListener('waiting', function() {
                showLoading();
            });

            // 可以播放事件
            videoElement.addEventListener('canplay', function() {
                hideLoading();
            });

            console.log('视频播放器初始化完成');
        });
    </script>
</body>
</html>

