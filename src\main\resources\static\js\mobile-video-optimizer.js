/**
 * 移动端视频播放优化工具
 * Mobile Video Player Optimizer
 * <AUTHOR>
 * @version 1.0.0
 */

class MobileVideoOptimizer {
    constructor(videoElement) {
        this.videoElement = videoElement;
        this.isMobile = this.detectMobile();
        this.isIOS = this.detectIOS();
        this.isAndroid = this.detectAndroid();
        this.retryCount = 0;
        this.maxRetries = 3;
        this.loadTimeout = null;
        
        this.init();
    }

    /**
     * 检测移动设备
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 检测iOS设备
     */
    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * 检测Android设备
     */
    detectAndroid() {
        return /Android/i.test(navigator.userAgent);
    }

    /**
     * 初始化移动端优化
     */
    init() {
        if (!this.isMobile) {
            console.log('非移动设备，跳过移动端优化');
            return;
        }

        console.log('初始化移动端视频优化:', {
            isMobile: this.isMobile,
            isIOS: this.isIOS,
            isAndroid: this.isAndroid,
            userAgent: navigator.userAgent
        });

        this.optimizeVideoElement();
        this.addMobileEventListeners();
        this.setupNetworkOptimization();
    }

    /**
     * 优化视频元素属性
     */
    optimizeVideoElement() {
        // 基础移动端属性
        this.videoElement.setAttribute('playsinline', 'true');
        this.videoElement.setAttribute('webkit-playsinline', 'true');
        this.videoElement.setAttribute('x5-video-player-type', 'h5');
        this.videoElement.setAttribute('x5-video-player-fullscreen', 'true');
        this.videoElement.setAttribute('x5-video-orientation', 'portraint');

        // iOS特殊处理
        if (this.isIOS) {
            this.videoElement.setAttribute('muted', 'true'); // iOS需要静音才能自动播放
            this.videoElement.preload = 'none'; // iOS上减少预加载
        }

        // Android特殊处理
        if (this.isAndroid) {
            this.videoElement.setAttribute('preload', 'metadata');
        }

        // 设置跨域
        this.videoElement.crossOrigin = 'anonymous';

        console.log('移动端视频元素优化完成');
    }

    /**
     * 添加移动端事件监听器
     */
    addMobileEventListeners() {
        // 触摸事件优化
        this.videoElement.addEventListener('touchstart', (e) => {
            // 防止双击缩放
            e.preventDefault();
        }, { passive: false });

        // iOS Safari 播放优化
        if (this.isIOS) {
            this.videoElement.addEventListener('click', () => {
                if (this.videoElement.paused) {
                    this.playWithFallback();
                }
            });
        }

        // 网络状态监听
        window.addEventListener('online', () => {
            console.log('网络已连接，尝试重新加载视频');
            this.retryLoad();
        });

        window.addEventListener('offline', () => {
            console.log('网络已断开');
            this.handleNetworkError();
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.videoElement.pause();
            }
        });
    }

    /**
     * 设置网络优化
     */
    setupNetworkOptimization() {
        // 检测网络类型
        if ('connection' in navigator) {
            const connection = navigator.connection;
            console.log('网络信息:', {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt
            });

            // 根据网络类型调整策略
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                this.videoElement.preload = 'none';
                console.log('检测到慢速网络，禁用预加载');
            }
        }
    }

    /**
     * 带回退的播放方法
     */
    async playWithFallback() {
        try {
            await this.videoElement.play();
            console.log('视频播放成功');
        } catch (error) {
            console.warn('自动播放失败:', error);
            
            if (this.isIOS && this.videoElement.muted) {
                // iOS上尝试取消静音后播放
                this.videoElement.muted = false;
                try {
                    await this.videoElement.play();
                    console.log('取消静音后播放成功');
                } catch (retryError) {
                    console.error('播放失败:', retryError);
                    this.showPlayButton();
                }
            } else {
                this.showPlayButton();
            }
        }
    }

    /**
     * 显示播放按钮
     */
    showPlayButton() {
        const playButton = document.createElement('div');
        playButton.className = 'mobile-play-button';
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 1000;
        `;

        playButton.addEventListener('click', () => {
            this.videoElement.play();
            playButton.remove();
        });

        this.videoElement.parentNode.appendChild(playButton);
    }

    /**
     * 重试加载
     */
    retryLoad() {
        if (this.retryCount >= this.maxRetries) {
            console.error('重试次数已达上限');
            return;
        }

        this.retryCount++;
        console.log(`第 ${this.retryCount} 次重试加载视频`);

        // 清除当前源
        this.videoElement.innerHTML = '';
        
        // 重新设置源
        const source = document.createElement('source');
        source.src = this.videoElement.dataset.videoUrl;
        source.type = 'video/mp4';
        this.videoElement.appendChild(source);

        // 重新加载
        this.videoElement.load();
    }

    /**
     * 处理网络错误
     */
    handleNetworkError() {
        if (typeof showError === 'function') {
            showError('网络连接已断开，请检查网络设置');
        }
    }

    /**
     * 设置加载超时
     */
    setLoadTimeout(timeout = 30000) {
        this.clearLoadTimeout();
        this.loadTimeout = setTimeout(() => {
            console.warn('视频加载超时');
            if (typeof showError === 'function') {
                showError('视频加载超时，请检查网络连接或稍后重试');
            }
        }, timeout);
    }

    /**
     * 清除加载超时
     */
    clearLoadTimeout() {
        if (this.loadTimeout) {
            clearTimeout(this.loadTimeout);
            this.loadTimeout = null;
        }
    }

    /**
     * 获取优化建议
     */
    getOptimizationTips() {
        const tips = [];

        if (this.isMobile) {
            tips.push('建议使用WiFi网络观看视频');
        }

        if (this.isIOS) {
            tips.push('iOS设备可能需要手动点击播放');
        }

        if ('connection' in navigator) {
            const connection = navigator.connection;
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                tips.push('检测到慢速网络，建议选择较低画质');
            }
        }

        return tips;
    }
}

// 全局导出
window.MobileVideoOptimizer = MobileVideoOptimizer;
