package com.videoplayer.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * OSS视频服务类
 * 处理阿里云OSS视频URL优化和移动端适配
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class OssVideoService {

    private static final Logger logger = LoggerFactory.getLogger(OssVideoService.class);

    /**
     * 优化OSS视频URL，添加移动端适配参数
     */
    public String optimizeVideoUrl(String originalUrl, boolean isMobile) {
        if (originalUrl == null || originalUrl.trim().isEmpty()) {
            return originalUrl;
        }

        try {
            // 检查是否为阿里云OSS URL
            if (!isOssUrl(originalUrl)) {
                logger.debug("非OSS URL，直接返回: {}", originalUrl);
                return originalUrl;
            }

            URL url = new URL(originalUrl);
            StringBuilder optimizedUrl = new StringBuilder();
            optimizedUrl.append(url.getProtocol()).append("://")
                       .append(url.getHost())
                       .append(url.getPath());

            // 添加优化参数
            Map<String, String> params = new HashMap<>();
            
            // 移动端优化参数
            if (isMobile) {
                // 移动端使用较低的画质以提高加载速度
                params.put("x-oss-process", "video/snapshot,t_1000,f_jpg,w_300,h_200,m_fast");
                // 启用移动端优化
                params.put("x-oss-traffic-limit", "838860800"); // 限制带宽为100MB/s
            }

            // 添加CORS支持
            params.put("response-content-type", "video/mp4");
            
            // 添加缓存控制
            params.put("response-cache-control", "max-age=3600");

            // 构建查询参数
            if (!params.isEmpty()) {
                optimizedUrl.append("?");
                boolean first = true;
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (!first) {
                        optimizedUrl.append("&");
                    }
                    optimizedUrl.append(entry.getKey()).append("=").append(entry.getValue());
                    first = false;
                }
            }

            String result = optimizedUrl.toString();
            logger.debug("OSS URL优化完成: {} -> {}", originalUrl, result);
            return result;

        } catch (Exception e) {
            logger.warn("OSS URL优化失败，返回原始URL: {}", originalUrl, e);
            return originalUrl;
        }
    }

    /**
     * 生成OSS视频缩略图URL
     */
    public String generateThumbnailUrl(String videoUrl) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return null;
        }

        try {
            if (!isOssUrl(videoUrl)) {
                return null;
            }

            URL url = new URL(videoUrl);
            StringBuilder thumbnailUrl = new StringBuilder();
            thumbnailUrl.append(url.getProtocol()).append("://")
                       .append(url.getHost())
                       .append(url.getPath());

            // 添加视频截图参数
            thumbnailUrl.append("?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_450,m_fast");

            String result = thumbnailUrl.toString();
            logger.debug("生成缩略图URL: {}", result);
            return result;

        } catch (Exception e) {
            logger.warn("生成缩略图URL失败: {}", videoUrl, e);
            return null;
        }
    }

    /**
     * 检查URL是否为阿里云OSS URL
     */
    private boolean isOssUrl(String url) {
        if (url == null) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains("aliyuncs.com") || 
               lowerUrl.contains("oss-") ||
               lowerUrl.contains(".oss.");
    }

    /**
     * 获取移动端优化的视频URL
     */
    public String getMobileOptimizedUrl(String originalUrl) {
        return optimizeVideoUrl(originalUrl, true);
    }

    /**
     * 获取桌面端优化的视频URL
     */
    public String getDesktopOptimizedUrl(String originalUrl) {
        return optimizeVideoUrl(originalUrl, false);
    }

    /**
     * 检查视频URL的可访问性
     */
    public boolean isVideoAccessible(String videoUrl) {
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return false;
        }

        try {
            URL url = new URL(videoUrl);
            // 这里可以添加实际的网络检查逻辑
            // 为了简化，现在只检查URL格式
            return url.getProtocol().startsWith("http");
        } catch (Exception e) {
            logger.warn("检查视频URL可访问性失败: {}", videoUrl, e);
            return false;
        }
    }

    /**
     * 获取视频格式信息
     */
    public Map<String, Object> getVideoInfo(String videoUrl) {
        Map<String, Object> info = new HashMap<>();
        
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return info;
        }

        try {
            // 从URL中提取文件扩展名
            String extension = getFileExtension(videoUrl);
            info.put("format", extension);
            info.put("isOss", isOssUrl(videoUrl));
            info.put("accessible", isVideoAccessible(videoUrl));
            
            // 如果是OSS URL，添加更多信息
            if (isOssUrl(videoUrl)) {
                info.put("thumbnailUrl", generateThumbnailUrl(videoUrl));
                info.put("mobileOptimizedUrl", getMobileOptimizedUrl(videoUrl));
                info.put("desktopOptimizedUrl", getDesktopOptimizedUrl(videoUrl));
            }

        } catch (Exception e) {
            logger.warn("获取视频信息失败: {}", videoUrl, e);
        }

        return info;
    }

    /**
     * 从URL中提取文件扩展名
     */
    private String getFileExtension(String url) {
        try {
            URL urlObj = new URL(url);
            String path = urlObj.getPath();
            int lastDot = path.lastIndexOf('.');
            if (lastDot > 0 && lastDot < path.length() - 1) {
                return path.substring(lastDot + 1).toLowerCase();
            }
        } catch (Exception e) {
            logger.debug("无法从URL提取扩展名: {}", url);
        }
        return "mp4"; // 默认返回mp4
    }

    /**
     * 生成适合移动端的视频播放配置
     */
    public Map<String, Object> getMobilePlaybackConfig(String videoUrl) {
        Map<String, Object> config = new HashMap<>();
        
        config.put("preload", "none"); // 移动端不预加载
        config.put("playsinline", true); // 内联播放
        config.put("controls", true); // 显示控制条
        config.put("muted", false); // 不静音（除非iOS需要）
        config.put("autoplay", false); // 不自动播放
        
        // OSS特殊配置
        if (isOssUrl(videoUrl)) {
            config.put("crossorigin", "anonymous"); // 跨域支持
            config.put("optimizedUrl", getMobileOptimizedUrl(videoUrl));
        }
        
        return config;
    }

    /**
     * 生成适合桌面端的视频播放配置
     */
    public Map<String, Object> getDesktopPlaybackConfig(String videoUrl) {
        Map<String, Object> config = new HashMap<>();
        
        config.put("preload", "metadata"); // 预加载元数据
        config.put("controls", true); // 显示控制条
        config.put("muted", false); // 不静音
        config.put("autoplay", false); // 不自动播放
        
        // OSS特殊配置
        if (isOssUrl(videoUrl)) {
            config.put("crossorigin", "anonymous"); // 跨域支持
            config.put("optimizedUrl", getDesktopOptimizedUrl(videoUrl));
        }
        
        return config;
    }
}
