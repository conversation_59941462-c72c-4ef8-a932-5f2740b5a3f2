<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端视频播放测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .test-video {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background: #000;
            border-radius: 8px;
        }
        
        .debug-info {
            font-family: monospace;
            font-size: 0.8rem;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-mobile-alt me-2"></i>
            移动端视频播放测试
        </h1>
        
        <!-- 设备信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">设备信息</h5>
            </div>
            <div class="card-body">
                <div class="debug-info" id="device-info">
                    正在检测设备信息...
                </div>
            </div>
        </div>
        
        <!-- 测试视频 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">测试视频播放</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <video 
                        id="test-video" 
                        class="test-video"
                        controls 
                        preload="none"
                        playsinline
                        webkit-playsinline
                        crossorigin="anonymous">
                        <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                
                <div class="text-center">
                    <button class="btn btn-primary me-2" onclick="testPlay()">
                        <i class="fas fa-play me-1"></i>测试播放
                    </button>
                    <button class="btn btn-secondary me-2" onclick="testPause()">
                        <i class="fas fa-pause me-1"></i>暂停
                    </button>
                    <button class="btn btn-info" onclick="runDiagnostics()">
                        <i class="fas fa-stethoscope me-1"></i>运行诊断
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 诊断结果 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">诊断结果</h5>
            </div>
            <div class="card-body">
                <div id="diagnostics-result">
                    点击"运行诊断"按钮开始检测...
                </div>
            </div>
        </div>
        
        <!-- 网络信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">网络信息</h5>
            </div>
            <div class="card-body">
                <div class="debug-info" id="network-info">
                    正在检测网络信息...
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isAndroid = /Android/i.test(userAgent);
            
            return {
                userAgent,
                isMobile,
                isIOS,
                isAndroid,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            };
        }
        
        // 网络检测
        function detectNetwork() {
            const info = {
                onLine: navigator.onLine,
                connection: null
            };
            
            if ('connection' in navigator) {
                const conn = navigator.connection;
                info.connection = {
                    effectiveType: conn.effectiveType,
                    downlink: conn.downlink,
                    rtt: conn.rtt,
                    saveData: conn.saveData
                };
            }
            
            return info;
        }
        
        // 显示设备信息
        function displayDeviceInfo() {
            const device = detectDevice();
            const deviceInfoEl = document.getElementById('device-info');
            
            deviceInfoEl.innerHTML = `
                <strong>User Agent:</strong> ${device.userAgent}<br>
                <strong>是否移动设备:</strong> ${device.isMobile ? '是' : '否'}<br>
                <strong>是否iOS:</strong> ${device.isIOS ? '是' : '否'}<br>
                <strong>是否Android:</strong> ${device.isAndroid ? '是' : '否'}<br>
                <strong>平台:</strong> ${device.platform}<br>
                <strong>语言:</strong> ${device.language}<br>
                <strong>Cookie启用:</strong> ${device.cookieEnabled ? '是' : '否'}<br>
                <strong>在线状态:</strong> ${device.onLine ? '在线' : '离线'}
            `;
        }
        
        // 显示网络信息
        function displayNetworkInfo() {
            const network = detectNetwork();
            const networkInfoEl = document.getElementById('network-info');
            
            let html = `<strong>在线状态:</strong> ${network.onLine ? '在线' : '离线'}<br>`;
            
            if (network.connection) {
                html += `
                    <strong>网络类型:</strong> ${network.connection.effectiveType}<br>
                    <strong>下行带宽:</strong> ${network.connection.downlink} Mbps<br>
                    <strong>往返时间:</strong> ${network.connection.rtt} ms<br>
                    <strong>省流模式:</strong> ${network.connection.saveData ? '开启' : '关闭'}
                `;
            } else {
                html += '<strong>网络API:</strong> 不支持';
            }
            
            networkInfoEl.innerHTML = html;
        }
        
        // 测试播放
        function testPlay() {
            const video = document.getElementById('test-video');
            video.play().then(() => {
                console.log('播放成功');
                showStatus('播放成功', 'success');
            }).catch(error => {
                console.error('播放失败:', error);
                showStatus('播放失败: ' + error.message, 'error');
            });
        }
        
        // 测试暂停
        function testPause() {
            const video = document.getElementById('test-video');
            video.pause();
            showStatus('已暂停', 'warning');
        }
        
        // 显示状态
        function showStatus(message, type) {
            const statusClass = `status-${type}`;
            const html = `<span class="status-indicator ${statusClass}"></span>${message}`;
            
            // 创建临时提示
            const alert = document.createElement('div');
            alert.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'warning'} alert-dismissible fade show`;
            alert.innerHTML = `
                ${html}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.container').insertBefore(alert, document.querySelector('.container').firstChild);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
        
        // 运行诊断
        function runDiagnostics() {
            const video = document.getElementById('test-video');
            const device = detectDevice();
            const network = detectNetwork();
            const resultEl = document.getElementById('diagnostics-result');
            
            let diagnostics = [];
            
            // 检查视频支持
            if (video.canPlayType('video/mp4')) {
                diagnostics.push('<span class="status-indicator status-success"></span>MP4格式支持: 是');
            } else {
                diagnostics.push('<span class="status-indicator status-error"></span>MP4格式支持: 否');
            }
            
            // 检查移动端属性
            if (device.isMobile) {
                if (video.hasAttribute('playsinline')) {
                    diagnostics.push('<span class="status-indicator status-success"></span>playsinline属性: 已设置');
                } else {
                    diagnostics.push('<span class="status-indicator status-warning"></span>playsinline属性: 未设置');
                }
                
                if (video.hasAttribute('webkit-playsinline')) {
                    diagnostics.push('<span class="status-indicator status-success"></span>webkit-playsinline属性: 已设置');
                } else {
                    diagnostics.push('<span class="status-indicator status-warning"></span>webkit-playsinline属性: 未设置');
                }
            }
            
            // 检查网络状态
            if (network.onLine) {
                diagnostics.push('<span class="status-indicator status-success"></span>网络连接: 正常');
            } else {
                diagnostics.push('<span class="status-indicator status-error"></span>网络连接: 断开');
            }
            
            // 检查跨域设置
            if (video.hasAttribute('crossorigin')) {
                diagnostics.push('<span class="status-indicator status-success"></span>跨域设置: 已配置');
            } else {
                diagnostics.push('<span class="status-indicator status-warning"></span>跨域设置: 未配置');
            }
            
            resultEl.innerHTML = diagnostics.join('<br>');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayDeviceInfo();
            displayNetworkInfo();
            
            const video = document.getElementById('test-video');
            
            // 添加视频事件监听
            video.addEventListener('loadstart', () => console.log('开始加载'));
            video.addEventListener('loadedmetadata', () => console.log('元数据加载完成'));
            video.addEventListener('canplay', () => console.log('可以播放'));
            video.addEventListener('error', (e) => console.error('视频错误:', e));
            
            // 网络状态变化监听
            window.addEventListener('online', () => {
                showStatus('网络已连接', 'success');
                displayNetworkInfo();
            });
            
            window.addEventListener('offline', () => {
                showStatus('网络已断开', 'error');
                displayNetworkInfo();
            });
        });
    </script>
</body>
</html>
