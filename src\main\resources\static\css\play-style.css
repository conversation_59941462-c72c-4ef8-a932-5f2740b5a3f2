/**
 * 视频播放页面专用样式 - 响应式设计
 * Video Play Page Styles - Responsive Design
 */

/* 响应式视频播放器样式 */
#video-player {
    width: 100%;
    height: auto;
    max-width: 100%;
    background-color: #000;
    border-radius: 8px;
    object-fit: contain;
    display: block;
}

/* 播放器容器 - 响应式 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid #e9ecef;
}

/* 视频包装器 - 16:9 宽高比 */
.video-wrapper {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 宽高比 */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper #video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1px 0px !important;
}

/* 覆盖Bootstrap的mb-4类，确保margin设置生效 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    margin: 1px 0px !important;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* 特定选择器：确保video-title h3 mb-3的内容居中 */
.video-title.h3.mb-3 {
    text-align: center !important;
}


/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .video-player-container {
        margin: 0 auto 1rem;
        border-radius: 8px;
        max-width: 100%;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }

    .video-info {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
    }

    .video-title {
        font-size: 1.25rem;
        text-align: center;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .video-actions {
        width: 100%;
        text-align: center;
    }
}

/* 加载状态 */
.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    z-index: 10;
}

.video-loading .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.video-loading .loading-text {
    color: #ffffff;
    font-size: 0.9rem;
    text-align: center;
}

/* 错误状态 */
.video-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    color: #ffffff;
    z-index: 15;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}

.video-error h5 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.video-error p {
    color: #cccccc;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.video-error .btn {
    min-width: 120px;
}


/* 平板设备优化 */
@media (max-width: 991.98px) {
    .video-player-container {
        max-width: 500px;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }
}

@media (max-width: 576px) {
    /* 小屏幕设备完全响应式 */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .video-player-container {
        margin: 0 0 1rem 0;
        border-radius: 8px;
        max-width: 100%;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }

    .video-info {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }

    .video-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .video-description {
        font-size: 0.9rem;
    }

    /* 移动端加载和错误状态优化 */
    .video-loading .spinner-border {
        width: 2.5rem;
        height: 2.5rem;
    }

    .video-loading .loading-text {
        font-size: 0.8rem;
    }

    .video-error {
        padding: 1.5rem;
    }

    .video-error i {
        font-size: 2.5rem;
    }

    .video-error h5 {
        font-size: 1.1rem;
    }

    .video-error p {
        font-size: 0.85rem;
    }

    .video-error .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

/* 主容器样式调整 */
.container.my-4 {
    margin-top: 10px !important;
}

/* 页脚样式调整 */
.bg-dark.text-light.py-4.mt-5 {
    margin-top: 5px !important;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-title {
        color: #f7fafc;
    }


}



/* 视频描述样式 */
.video-description {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.video-description p {
    line-height: 1.6;
    color: #6c757d;
}

.video-wrapper {
    height: 380px!important;
    width: 100%!important;
}